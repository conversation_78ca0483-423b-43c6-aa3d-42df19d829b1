// Assuming ColorsRes is part of your project
import 'package:Zoom_Fresh_App/helper/styles/colorsRes.dart';
import 'package:flutter/material.dart';

class FreeDeliveryProgressBar extends StatelessWidget {
  /// The current total amount in the user's cart.
  /// Examples: "₹10", "150.50", "€ 2,500.00"
  final String currentAmount;

  /// The delivery charge amount. The progress bar will only show if this is > 0.
  /// Examples: "₹50", "0", "0.00"
  final String deliveryCharges;

  /// The amount required to be eligible for free delivery.
  final num freeDeliveryThreshold;

  /// The currency symbol to display (e.g., "₹", "$", "€").
  final String currencySymbol;

  /// Whether the user has an active subscription (hides progress bar if true).
  final bool hasActiveSubscription;

  const FreeDeliveryProgressBar({
    Key? key,
    required this.currentAmount,
    required this.deliveryCharges,
    required this.freeDeliveryThreshold,
    this.currencySymbol = '₹', // Default to Rupee symbol
    this.hasActiveSubscription = false,
  }) : super(key: key);

  /// A private helper to safely parse currency strings.
  /// It removes all non-numeric characters (except the dot) and returns a double.
  double _parseCurrency(String amount) {
    // Return 0 if the string is null or empty.
    if (amount.isEmpty) return 0.0;
    // Remove symbols, letters, spaces, and commas.
    final numericString = amount.replaceAll(RegExp(r'[^0-9.]'), '');
    // Safely parse, defaulting to 0.0 if the cleaned string is invalid.
    return double.tryParse(numericString) ?? 0.0;
  }

  @override
  Widget build(BuildContext context) {
    // Hide the widget if user has active subscription
    if (hasActiveSubscription) {
      return const SizedBox.shrink();
    }

    // --- FIX 1: Use the robust parser for all amounts ---
    final double parsedAmount = _parseCurrency(currentAmount);
    final double parsedDeliveryCharge = _parseCurrency(deliveryCharges);

    // --- FIX 2: Use Guard Clauses for clearer logic ---
    // Hide the widget if any of these conditions are met:
    // 1. Delivery is already free.
    // 2. The user has already met the free delivery threshold.
    // 3. The threshold is not a valid positive number (to prevent division by zero).
    // if (parsedDeliveryCharge <= 0 || parsedAmount >= freeDeliveryThreshold || freeDeliveryThreshold <= 0) {
    //   return const SizedBox.shrink();
    // }
    if (parsedAmount >= freeDeliveryThreshold || freeDeliveryThreshold <= 0) {
      return const SizedBox.shrink();
    }

    // --- All calculations are now safe and clear ---
    final double remainingAmount = freeDeliveryThreshold - parsedAmount;
    final double progress = parsedAmount / freeDeliveryThreshold;

    final theme = Theme.of(context);

    return Padding(
      // Added padding to the entire component for better spacing.
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: progress),
            duration: const Duration(milliseconds: 500),
            builder: (context, animatedValue, child) {
              return ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                child: LinearProgressIndicator(
                  value: animatedValue,
                  minHeight: 10,
                  backgroundColor: theme.primaryColor.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(theme.primaryColor),
                ),
              );
            },
          ),
          const SizedBox(height: 8.0),
          RichText(
            text: TextSpan(
              // Use a single consistent style from the theme.
              style: theme.textTheme.bodyMedium?.copyWith(
                color: ColorsRes.mainTextColor,
              ),
              children: <TextSpan>[
                const TextSpan(text: 'Add '),
                TextSpan(
                  // --- FIX 3: Use the dynamic currency symbol ---
                  text: '$currencySymbol${remainingAmount.toStringAsFixed(0)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: theme.primaryColor, // Using theme.primaryColor for consistency
                  ),
                ),
                const TextSpan(text: ' more to get FREE Delivery'),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
