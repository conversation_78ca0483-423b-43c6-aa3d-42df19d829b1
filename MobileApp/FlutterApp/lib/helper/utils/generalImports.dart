export 'dart:async';
export 'dart:convert';
export 'dart:io';
export 'dart:math';
export 'dart:typed_data';

export 'package:Zoom_Fresh_App/firebase_options.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/OrderTrackingHistoryBottomSheet.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/bottomSheetLanguageListContainer.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/bottomSheetThemeListContainer.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/brandItemContainer.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/cartListItemContainer.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/cartOverlayCardWidget.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/categoryItemContainer.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/countryItemContainer.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/customCheckbox.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/customCircularProgressIndicator.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/customRadio.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/customShimmer.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/customTextLabel.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/defaultBlankItemMessageScreen.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/editBoxWidget.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/expandableText.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/messageContainer.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/noInternetConnectionScreen.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/paymentMethodWidget.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/permissionHandlerBottomSheet.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/productCartButton.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/productGridItemContainer.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/productListItemContainer.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/productListRatingBuilderWidget.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/productVariantDropDownMenuGrid.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/productVariantDropDownMenuList.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/productWishListIcon.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/scrollGlowBehavior.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/sellerItemContainer.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/subscribebanner2.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/subscriptionbanner.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/titleHeaderWithViewAllOption.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/toastAnimation.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/trackMyOrderButton.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/widgets.dart';
export 'package:Zoom_Fresh_App/helper/generalWidgets/widgets2.dart';
export 'package:Zoom_Fresh_App/helper/sessionManager.dart';
export 'package:Zoom_Fresh_App/helper/styles/colorsRes.dart';
export 'package:Zoom_Fresh_App/helper/styles/dashedRect.dart';
export 'package:Zoom_Fresh_App/helper/styles/designConfig.dart';
export 'package:Zoom_Fresh_App/helper/utils/apiAndParams.dart';
export 'package:Zoom_Fresh_App/helper/utils/awsomeNotification.dart';
export 'package:Zoom_Fresh_App/helper/utils/barcodeScanner.dart';
export 'package:Zoom_Fresh_App/helper/utils/constant.dart';
export 'package:Zoom_Fresh_App/helper/utils/generalMethods.dart';
export 'package:Zoom_Fresh_App/helper/utils/mapDeliveredMarker.dart';
export 'package:Zoom_Fresh_App/helper/utils/markergenerator.dart';
export 'package:Zoom_Fresh_App/helper/utils/routeGenerator.dart';
export 'package:Zoom_Fresh_App/helper/utils/stripeService.dart';
export 'package:Zoom_Fresh_App/main.dart';
export 'package:Zoom_Fresh_App/models/address.dart';
export 'package:Zoom_Fresh_App/models/cart.dart';
export 'package:Zoom_Fresh_App/models/cartList.dart';
export 'package:Zoom_Fresh_App/models/checkout.dart';
export 'package:Zoom_Fresh_App/models/faq.dart';
export 'package:Zoom_Fresh_App/models/geoAddress.dart';
export 'package:Zoom_Fresh_App/models/homeScreenData.dart';
export 'package:Zoom_Fresh_App/models/initiateTransaction.dart';
export 'package:Zoom_Fresh_App/models/languageList.dart';
export 'package:Zoom_Fresh_App/models/liveOrderTracking.dart';
export 'package:Zoom_Fresh_App/models/notification.dart';
export 'package:Zoom_Fresh_App/models/notificationSettings.dart';
export 'package:Zoom_Fresh_App/models/order.dart';
export 'package:Zoom_Fresh_App/models/paymentMethods.dart';
export 'package:Zoom_Fresh_App/models/paytmTransactionToken.dart';
export 'package:Zoom_Fresh_App/models/placedPrePaidOrder.dart';
export 'package:Zoom_Fresh_App/models/productDetail.dart';
export 'package:Zoom_Fresh_App/models/productList.dart';
export 'package:Zoom_Fresh_App/models/productListItem.dart';
export 'package:Zoom_Fresh_App/models/productRating.dart';
export 'package:Zoom_Fresh_App/models/promoCode.dart';
export 'package:Zoom_Fresh_App/models/settings.dart';
export 'package:Zoom_Fresh_App/models/timeSlots.dart';
export 'package:Zoom_Fresh_App/models/transaction.dart';
export 'package:Zoom_Fresh_App/models/walletHistory.dart';
export 'package:Zoom_Fresh_App/provider/SellerProvider.dart';
export 'package:Zoom_Fresh_App/provider/activeOrdersProvider.dart';
export 'package:Zoom_Fresh_App/provider/addressListProvider.dart';
export 'package:Zoom_Fresh_App/provider/appLanguageProvider.dart';
export 'package:Zoom_Fresh_App/provider/appSettingsProvider.dart';
export 'package:Zoom_Fresh_App/provider/brandProvider.dart';
export 'package:Zoom_Fresh_App/provider/cartListProvider.dart';
export 'package:Zoom_Fresh_App/provider/cartProvider.dart';
export 'package:Zoom_Fresh_App/provider/categoryProvider.dart';
export 'package:Zoom_Fresh_App/provider/checkoutProvider.dart';
export 'package:Zoom_Fresh_App/provider/cityByLatLongProvider.dart';
export 'package:Zoom_Fresh_App/provider/contactprovider.dart';
export 'package:Zoom_Fresh_App/provider/countryProvider.dart';
export 'package:Zoom_Fresh_App/provider/currentOrderProvider.dart';
export 'package:Zoom_Fresh_App/provider/faqListProvider.dart';
export 'package:Zoom_Fresh_App/provider/homeMainScreenProvider.dart';
export 'package:Zoom_Fresh_App/provider/homeScreenDataProvider.dart';
export 'package:Zoom_Fresh_App/provider/liveOrderTrackingProvider.dart';
export 'package:Zoom_Fresh_App/provider/notificationListProvider.dart';
export 'package:Zoom_Fresh_App/provider/notificationsSettingsProvider.dart';
export 'package:Zoom_Fresh_App/provider/orderInvoiceProvider.dart';
export 'package:Zoom_Fresh_App/provider/paymentMethodsProvider.dart';
export 'package:Zoom_Fresh_App/provider/previousOrdersProvider.dart';
export 'package:Zoom_Fresh_App/provider/productChangeListingProvider.dart';
export 'package:Zoom_Fresh_App/provider/productDetailProvider.dart';
export 'package:Zoom_Fresh_App/provider/productFilterProvider.dart';
export 'package:Zoom_Fresh_App/provider/productListProvider.dart';
export 'package:Zoom_Fresh_App/provider/productSearchProvider.dart';
export 'package:Zoom_Fresh_App/provider/productWishListProvider.dart';
export 'package:Zoom_Fresh_App/provider/promoCodeProvider.dart';
export 'package:Zoom_Fresh_App/provider/ratingProvider.dart';
export 'package:Zoom_Fresh_App/provider/selectedVariantItemProvider.dart';
export 'package:Zoom_Fresh_App/provider/showHidePassword.dart';
export 'package:Zoom_Fresh_App/provider/sliderImagesProvider.dart';
export 'package:Zoom_Fresh_App/provider/subscribeplanscreen.dart';
export 'package:Zoom_Fresh_App/provider/subscriptionProvider.dart';
export 'package:Zoom_Fresh_App/provider/themeProvider.dart';
export 'package:Zoom_Fresh_App/provider/transactionListProvider.dart';
export 'package:Zoom_Fresh_App/provider/updateOrderStatusProvider.dart';
export 'package:Zoom_Fresh_App/provider/userProfileProvider.dart';
export 'package:Zoom_Fresh_App/provider/voiceToTextProvider.dart';
export 'package:Zoom_Fresh_App/provider/walletHistoryListProvider.dart';
export 'package:Zoom_Fresh_App/provider/walletRechargeProvider.dart';
export 'package:Zoom_Fresh_App/repositories/addTransactionApi.dart';
export 'package:Zoom_Fresh_App/repositories/addressApi.dart';
export 'package:Zoom_Fresh_App/repositories/appLanguageApi.dart';
export 'package:Zoom_Fresh_App/repositories/appSettingsApi.dart';
export 'package:Zoom_Fresh_App/repositories/brandApi.dart';
export 'package:Zoom_Fresh_App/repositories/cartApi.dart';
export 'package:Zoom_Fresh_App/repositories/categoryApi.dart';
export 'package:Zoom_Fresh_App/repositories/cityByLatLongApi.dart';
export 'package:Zoom_Fresh_App/repositories/deleteUserAccountApi.dart';
export 'package:Zoom_Fresh_App/repositories/faqApi.dart';
export 'package:Zoom_Fresh_App/repositories/homeScreenApi.dart';
export 'package:Zoom_Fresh_App/repositories/initiateTransactionApi.dart';
export 'package:Zoom_Fresh_App/repositories/loginApi.dart';
export 'package:Zoom_Fresh_App/repositories/notificationApi.dart';
export 'package:Zoom_Fresh_App/repositories/notificationSettingsApi.dart';
export 'package:Zoom_Fresh_App/repositories/orderInvoiceApi.dart';
export 'package:Zoom_Fresh_App/repositories/ordersApi.dart';
export 'package:Zoom_Fresh_App/repositories/paymentMethodsSettingsApi.dart';
export 'package:Zoom_Fresh_App/repositories/placeOrderApi.dart';
export 'package:Zoom_Fresh_App/repositories/productApi.dart';
export 'package:Zoom_Fresh_App/repositories/promoCodeApi.dart';
export 'package:Zoom_Fresh_App/repositories/registerFcmKey.dart';
export 'package:Zoom_Fresh_App/repositories/timeSlotSettingsApi.dart';
export 'package:Zoom_Fresh_App/repositories/transactionApi.dart';
export 'package:Zoom_Fresh_App/repositories/updateProfileApi.dart';
export 'package:Zoom_Fresh_App/repositories/userDetailApi.dart';
export 'package:Zoom_Fresh_App/screens/appUpdateScreen.dart';
export 'package:Zoom_Fresh_App/screens/brandListScreen.dart';
export 'package:Zoom_Fresh_App/screens/cartListScreen/cartListScreen.dart';
export 'package:Zoom_Fresh_App/screens/cartListScreen/screens/promoCodeScreen/promoCodeScreen.dart';
export 'package:Zoom_Fresh_App/screens/cartListScreen/screens/promoCodeScreen/widget/customPromoCodeDialog.dart';
export 'package:Zoom_Fresh_App/screens/cashfreePaymentScreen.dart';
export 'package:Zoom_Fresh_App/screens/checkoutScreen/checkoutScreen.dart';
export 'package:Zoom_Fresh_App/screens/checkoutScreen/widget/addressWidget.dart';
export 'package:Zoom_Fresh_App/screens/checkoutScreen/widget/deliveryChargesWidget.dart';
export 'package:Zoom_Fresh_App/screens/checkoutScreen/widget/orderNoteWidget.dart';
export 'package:Zoom_Fresh_App/screens/checkoutScreen/widget/placeOrderButtonWidget.dart';
export 'package:Zoom_Fresh_App/screens/checkoutScreen/widget/timeSlotsWidget.dart';
export 'package:Zoom_Fresh_App/screens/confirmLocationScreen/confirmLocationScreen.dart';
export 'package:Zoom_Fresh_App/screens/confirmLocationScreen/widget/confirmButtonWidget.dart';
export 'package:Zoom_Fresh_App/screens/countryListScreen.dart';
export 'package:Zoom_Fresh_App/screens/editProfileScreen.dart';
export 'package:Zoom_Fresh_App/screens/forgotPasswordScreen.dart';
export 'package:Zoom_Fresh_App/screens/introSliderScreen.dart';
export 'package:Zoom_Fresh_App/screens/loginAccountScreen/loginAccountScreen.dart';
export 'package:Zoom_Fresh_App/screens/loginAccountScreen/widget/backgorundImageWidget.dart';
export 'package:Zoom_Fresh_App/screens/loginAccountScreen/widget/backgorundOverlayImageWidget.dart';
export 'package:Zoom_Fresh_App/screens/loginAccountScreen/widget/buildDottedDivider.dart';
export 'package:Zoom_Fresh_App/screens/loginAccountScreen/widget/socialMediaLoginButtonWidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/categoryListScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/homeScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/brandWidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/categoryWidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/countryOfOriginWidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/customDialog.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/cutselection.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/deliverAddressWidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/flashsale.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/floatingcallwidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/homeScreenProductListItem.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/homeScreenProductListItem_H.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/offerImagesWidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/sectionProductListingStyle1.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/sectionProductListingStyle1H.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/sectionProductListingStyle2.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/sectionProductListingStyle3.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/sectionProductListingStyle4.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/sectionWidget%20copy.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/sectionWidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/sellerWidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/sliderImageWidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/homeScreen/widget/subscriptionbottomwidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/mainHomeScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/profileMenuScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/screens/addressScreen/addressDetailScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/screens/addressScreen/addressListScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/screens/faqListScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/screens/notificationListScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/screens/notificationsAndMailSettingsScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/screens/transactionListScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/screens/walletHistoryListScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/screens/webViewScreen.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/widget/buttonWidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/widget/profileHeader.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/profileMenuScreen/widget/quickUseWidget.dart';
export 'package:Zoom_Fresh_App/screens/mainHomeScreen/wishListScreen.dart';
export 'package:Zoom_Fresh_App/screens/midtransPaymentScreen.dart';
export 'package:Zoom_Fresh_App/screens/orderDetailScreen/orderDetailScreen.dart';
export 'package:Zoom_Fresh_App/screens/orderDetailScreen/widgets/cancelProductDialog.dart';
export 'package:Zoom_Fresh_App/screens/orderDetailScreen/widgets/orderTrackerItemContainer.dart';
export 'package:Zoom_Fresh_App/screens/orderDetailScreen/widgets/returnProductDialog.dart';
export 'package:Zoom_Fresh_App/screens/orderDetailScreen/widgets/submitRatingWidget.dart';
export 'package:Zoom_Fresh_App/screens/orderPlacedScreen.dart';
export 'package:Zoom_Fresh_App/screens/orderTrackerScreen.dart';
export 'package:Zoom_Fresh_App/screens/ordersHistoryScreen/ordersHistoryScreen.dart';
export 'package:Zoom_Fresh_App/screens/ordersHistoryScreen/widgets/orderContainerShimmer.dart';
export 'package:Zoom_Fresh_App/screens/ordersHistoryScreen/widgets/orderItemContainer.dart';
export 'package:Zoom_Fresh_App/screens/ordersHistoryScreen/widgets/orderTypeButtonWidget.dart';
export 'package:Zoom_Fresh_App/screens/otpVerificationScreen.dart';
export 'package:Zoom_Fresh_App/screens/paypalPaymentScreen.dart';
export 'package:Zoom_Fresh_App/screens/paytabsPaymentScreen.dart';
export 'package:Zoom_Fresh_App/screens/phonePePaymentScreen.dart';
export 'package:Zoom_Fresh_App/screens/productDetailScreen/productDetailScreen.dart';
export 'package:Zoom_Fresh_App/screens/productDetailScreen/widget/otherImagesBoxDecoration.dart';
export 'package:Zoom_Fresh_App/screens/productDetailScreen/widget/productDetailAddToCartButtonWidget.dart';
export 'package:Zoom_Fresh_App/screens/productDetailScreen/widget/productDetailWidget.dart';
export 'package:Zoom_Fresh_App/screens/productFullScreenImagesScreen.dart';
export 'package:Zoom_Fresh_App/screens/productListFilterScreen/productListFilterScreen.dart';
export 'package:Zoom_Fresh_App/screens/productListFilterScreen/widget/brandWidget.dart';
export 'package:Zoom_Fresh_App/screens/productListFilterScreen/widget/priceRangeWidget.dart';
export 'package:Zoom_Fresh_App/screens/productListFilterScreen/widget/sizeWidget.dart';
export 'package:Zoom_Fresh_App/screens/productListScreen.dart';
export 'package:Zoom_Fresh_App/screens/ratingAndReviewScreen.dart';
export 'package:Zoom_Fresh_App/screens/ratingImageViewScreen.dart';
export 'package:Zoom_Fresh_App/screens/searchProductScreen/searchProductScreen.dart';
export 'package:Zoom_Fresh_App/screens/searchProductScreen/widget/speechToTextSearch.dart';
export 'package:Zoom_Fresh_App/screens/sellerListScreen.dart';
export 'package:Zoom_Fresh_App/screens/splashScreen.dart';
export 'package:Zoom_Fresh_App/screens/underMaintenanceScreen.dart';
export 'package:Zoom_Fresh_App/screens/walletRechargeScreen/walletRechargeScreen.dart';
export 'package:Zoom_Fresh_App/screens/walletRechargeScreen/widget/walletRechargeButtonWidget.dart';
export 'package:awesome_notifications/awesome_notifications.dart';
export 'package:cached_network_image/cached_network_image.dart';
export 'package:connectivity_plus/connectivity_plus.dart';
export 'package:country_code_picker/country_code_picker.dart';
export 'package:device_info_plus/device_info_plus.dart';
export 'package:dotted_border/dotted_border.dart';
export 'package:external_path/external_path.dart';
export 'package:firebase_auth/firebase_auth.dart';
export 'package:firebase_core/firebase_core.dart';
export 'package:firebase_messaging/firebase_messaging.dart';
export 'package:flutter/foundation.dart';
export 'package:flutter/gestures.dart';
export 'package:flutter/material.dart';
export 'package:flutter/rendering.dart';
export 'package:flutter/services.dart';
export 'package:flutter_paystack/flutter_paystack.dart';
export 'package:flutter_polyline_points/flutter_polyline_points.dart';
export 'package:flutter_rating_bar/flutter_rating_bar.dart';
export 'package:flutter_svg/flutter_svg.dart';
export 'package:google_fonts/google_fonts.dart';
export 'package:google_maps_flutter/google_maps_flutter.dart';
export 'package:http/http.dart';
export 'package:image_cropper/image_cropper.dart';
export 'package:image_picker/image_picker.dart';
export 'package:open_filex/open_filex.dart';
export 'package:package_info_plus/package_info_plus.dart';
export 'package:path_provider/path_provider.dart';
// export 'package:paytm_allinonesdk/paytm_allinonesdk.dart';
export 'package:paytmpayments_allinonesdk/paytmpayments_allinonesdk.dart';
export 'package:permission_handler/permission_handler.dart';
export 'package:photo_view/photo_view.dart';
export 'package:photo_view/photo_view_gallery.dart';
export 'package:pinput/pinput.dart';
export 'package:provider/provider.dart';
export 'package:razorpay_flutter/razorpay_flutter.dart';
export 'package:share_plus/share_plus.dart';
export 'package:shared_preferences/shared_preferences.dart';
export 'package:shimmer/shimmer.dart';
export 'package:speech_to_text/speech_recognition_error.dart';
export 'package:speech_to_text/speech_recognition_result.dart';
export 'package:speech_to_text/speech_to_text.dart';
export 'package:url_launcher/url_launcher.dart';
export 'package:version/version.dart';
export 'package:vibration/vibration.dart';
