import 'package:Zoom_Fresh_App/helper/utils/generalImports.dart';
import 'package:http/http.dart' as http;

class SubscriptionProvider extends ChangeNotifier {
  bool _isLoading = false;
  SubscriptionStatus? _subscriptionStatus;
  DateTime? _lastFetchTime;

  // Cache duration - 5 minutes
  static const Duration _cacheDuration = Duration(minutes: 5);

  bool get isLoading => _isLoading;
  SubscriptionStatus? get subscriptionStatus => _subscriptionStatus;

  /// Check if user has an active subscription
  bool get hasActiveSubscription {
    if (_subscriptionStatus?.subscriptionId == null) return false;

    // Check if subscription is still valid (not expired)
    if (_subscriptionStatus?.endDate != null) {
      final daysLeft = _calculateDaysLeft(_subscriptionStatus!.endDate!);
      return daysLeft > 0;
    }

    return true; // If no end date, assume active
  }

  /// Get subscription status with caching
  Future<void> fetchSubscriptionStatus({bool forceRefresh = false}) async {
    // Check cache validity
    if (!forceRefresh && _subscriptionStatus != null && _lastFetchTime != null && DateTime.now().difference(_lastFetchTime!) < _cacheDuration) {
      return; // Use cached data
    }

    _isLoading = true;
    notifyListeners();

    try {
      final String token = Constant.session.getData(SessionManager.keyToken);

      if (token.isEmpty) {
        _subscriptionStatus = SubscriptionStatus(
          message: "User not logged in.",
        );
        _isLoading = false;
        notifyListeners();
        return;
      }

      final status = await _checkSubscriptionStatus(token);
      _subscriptionStatus = status;
      _lastFetchTime = DateTime.now();
    } catch (e) {
      debugPrint('🚨 Failed to get subscription status: $e');
      _subscriptionStatus = SubscriptionStatus(
        message: "Error fetching status.",
      );
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<SubscriptionStatus> _checkSubscriptionStatus(String token) async {
    final url = Uri.parse('https://zoomfresh.co.in/customer/check_subscription');

    try {
      final response = await http.get(
        url,
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final decodedBody = json.decode(response.body) as Map<String, dynamic>;

      if (response.statusCode == 200) {
        final int apiStatus = decodedBody['status'] ?? 0;
        final String message = decodedBody['message'] ?? 'No message from server.';

        if (apiStatus == 1) {
          return SubscriptionStatus.fromActiveJson(decodedBody);
        } else {
          return SubscriptionStatus(message: message);
        }
      } else {
        final String errorMessage = decodedBody['message'] ?? 'Unknown server error.';
        throw Exception('API Error: Status ${response.statusCode}, Message: $errorMessage');
      }
    } catch (e) {
      debugPrint('An error occurred in _checkSubscriptionStatus: $e');
      rethrow;
    }
  }

  int _calculateDaysLeft(String dateString) {
    try {
      final expirationDate = DateTime.parse(dateString);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      return expirationDate.difference(today).inDays;
    } catch (e) {
      return -99999;
    }
  }

  /// Clear cached subscription status
  void clearCache() {
    _subscriptionStatus = null;
    _lastFetchTime = null;
    notifyListeners();
  }

  /// Refresh subscription status
  Future<void> refreshSubscriptionStatus() async {
    await fetchSubscriptionStatus(forceRefresh: true);
  }
}
