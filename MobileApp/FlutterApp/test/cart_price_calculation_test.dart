import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Cart Price Calculation Tests', () {
    
    // Helper function to replicate the cart price calculation logic
    double calculateCartItemPrice({
      required String qty,
      required String measurement,
      required String price,
      required String discountedPrice,
    }) {
      double requestedQuantity = double.tryParse(qty) ?? 1.0;
      double baseMeasurement = double.tryParse(measurement) ?? 1.0;
      double basePrice = double.parse(discountedPrice) != 0 
          ? double.parse(discountedPrice) 
          : double.parse(price);
      
      // Prevent division by zero
      if (baseMeasurement <= 0) {
        baseMeasurement = 1.0;
      }
      
      // Handle unit conversion: if baseMeasurement is in grams (>=500) and requestedQuantity appears to be in kg (<10)
      // Convert kg to grams for proper calculation
      double quantityRatio;
      if (baseMeasurement >= 500 && requestedQuantity < 10 && requestedQuantity > 0) {
        double requestedQuantityInGrams = requestedQuantity * 1000; // Convert kg to grams
        quantityRatio = requestedQuantityInGrams / baseMeasurement;
      } else {
        // For other cases, use direct ratio
        quantityRatio = requestedQuantity / baseMeasurement;
      }
      
      double totalPrice = basePrice * quantityRatio;
      return totalPrice;
    }

    test('Sardine price calculation - 0.5 kg should be ₹150', () {
      double result = calculateCartItemPrice(
        qty: "0.5",
        measurement: "500",
        price: "150",
        discountedPrice: "0",
      );
      
      expect(result, equals(150.0));
    });

    test('Sardine price calculation - 1.0 kg should be ₹300', () {
      double result = calculateCartItemPrice(
        qty: "1.0",
        measurement: "500",
        price: "150",
        discountedPrice: "0",
      );
      
      expect(result, equals(300.0));
    });

    test('Sardine price calculation - 1.5 kg should be ₹450', () {
      double result = calculateCartItemPrice(
        qty: "1.5",
        measurement: "500",
        price: "150",
        discountedPrice: "0",
      );
      
      expect(result, equals(450.0));
    });

    test('Sardine price calculation - 2.0 kg should be ₹600', () {
      double result = calculateCartItemPrice(
        qty: "2.0",
        measurement: "500",
        price: "150",
        discountedPrice: "0",
      );
      
      expect(result, equals(600.0));
    });

    test('Price calculation with discounted price', () {
      double result = calculateCartItemPrice(
        qty: "1.0",
        measurement: "500",
        price: "150",
        discountedPrice: "120",
      );
      
      expect(result, equals(240.0)); // 120 * 2 (1kg = 2 * 500gm)
    });

    test('Price calculation for non-gram measurements', () {
      double result = calculateCartItemPrice(
        qty: "2.0",
        measurement: "1",
        price: "100",
        discountedPrice: "0",
      );
      
      expect(result, equals(200.0)); // Simple multiplication for non-gram units
    });

    test('Edge case - zero measurement should default to 1', () {
      double result = calculateCartItemPrice(
        qty: "2.0",
        measurement: "0",
        price: "100",
        discountedPrice: "0",
      );
      
      expect(result, equals(200.0)); // Should use default measurement of 1
    });
  });
}
